{"name": "codetutor-ai", "version": "1.0.0", "description": "AI-powered animated tutorial generator", "private": true, "workspaces": ["frontend", "backend", "animation-engine"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:animation", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:animation": "cd animation-engine && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:animation": "cd animation-engine && npm run studio", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules animation-engine/node_modules", "setup": "npm run install:all && npm run setup:env", "setup:env": "echo 'GOOGLE_AI_API_KEY=AIzaSyDiDRECIUq61AG5MLv1MPVZrrPLAeSelks' > .env"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["ai", "tutorial", "animation", "coding", "education", "video-generation"], "author": "CodeTutor AI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/codetutor-ai.git"}}